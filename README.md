# BOSS直聘爬虫

🚀 **高效的BOSS直聘职位信息爬虫**，7个字段100%提取成功！

## ✅ 核心功能

- **7个字段完整提取** - 岗位名称、薪资、待遇、职位描述、公司简介、工作地点、网址
- **绕过安全检查** - 使用反反爬技术
- **批量处理** - 支持大量URL批量爬取
- **多格式输出** - JSON和Excel格式

## 🚀 快速使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 选择功能
- 爬取单个职位URL
- 批量爬取多个URL
- 从文件批量爬取

## � 输出格式

程序会自动生成：
- `boss_jobs_batch.json` - JSON格式数据
- `boss_jobs_batch.xlsx` - Excel格式数据

## 📁 主要文件

- `main.py` - 主程序入口
- `boss_crawler.py` - 核心爬虫引擎
- `boss_security_bypass.py` - 安全检查绕过器
- `ultimate_data_extractor.py` - 数据提取器
- `simple_crawler.py` - 批量爬取器
- `data_processor.py` - 数据处理器
- `requirements.txt` - 依赖列表

## ⚠️ 注意事项

- 仅用于学习和研究目的
- 请遵守网站使用协议
- 建议设置合理的请求间隔
