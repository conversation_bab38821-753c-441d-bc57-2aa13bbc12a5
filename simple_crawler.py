"""
简单批量爬取器
支持批量爬取和数据处理
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from boss_crawler import BOSSCrawler
from data_processor import data_processor

class SimpleCrawler:
    """简单批量爬取器"""
    
    def __init__(self):
        pass
        
    async def crawl_batch(self, urls: List[str], output_prefix: str = "boss_jobs_batch") -> Dict[str, Any]:
        """批量爬取"""
        start_time = time.time()
        
        print(f"🚀 开始批量爬取")
        print(f"📊 URL总数: {len(urls)}")
        print("=" * 50)
        
        successful_jobs = []
        failed_urls = []
        
        async with BOSSCrawler() as crawler:
            for i, url in enumerate(urls, 1):
                print(f"📍 爬取进度: {i}/{len(urls)} - {url}")
                
                try:
                    job_data = await crawler.crawl_job_detail(url)
                    
                    if job_data:
                        successful_jobs.append(job_data)
                        print(f"✅ 成功: {job_data.get('岗位名称', 'Unknown')}")
                    else:
                        failed_urls.append(url)
                        print(f"❌ 失败: {url}")
                        
                except Exception as e:
                    failed_urls.append(url)
                    print(f"❌ 异常: {url} - {str(e)}")
                
                # 请求间隔
                if i < len(urls):
                    await asyncio.sleep(2)
        
        # 处理结果
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            "total_count": len(urls),
            "success_count": len(successful_jobs),
            "failed_count": len(failed_urls),
            "success_rate": (len(successful_jobs) / len(urls)) * 100 if urls else 0,
            "duration": duration,
            "successful_jobs": successful_jobs,
            "failed_urls": failed_urls
        }
        
        # 保存数据
        if successful_jobs:
            result_files = data_processor.process_and_save(successful_jobs)
            result["json_file"] = result_files.get("json", "")
            result["excel_file"] = result_files.get("excel", "")
            
            print(f"\n📊 爬取完成统计:")
            print(f"  总计: {result['total_count']} 个URL")
            print(f"  成功: {result['success_count']} 个")
            print(f"  失败: {result['failed_count']} 个")
            print(f"  成功率: {result['success_rate']:.1f}%")
            print(f"  耗时: {duration:.1f} 秒")
            print(f"  JSON文件: {result['json_file']}")
            print(f"  Excel文件: {result['excel_file']}")
        else:
            print(f"\n❌ 没有成功爬取到任何数据")
        
        return result

# 全局实例
simple_crawler = SimpleCrawler()
